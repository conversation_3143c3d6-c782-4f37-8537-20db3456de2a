<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Additive Blending Demo</title>
    <style>
        body { margin: 0; background-color: #000; }
        canvas { display: block; }
        .lil-gui { position: absolute; top: 10px; right: 10px; }
    </style>
</head>
<body>
<script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
        }
    }
</script>
<script type="module">
  import * as THREE from 'three';
  import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
  import GUI from 'https://cdn.jsdelivr.net/npm/lil-gui@0.19/+esm';

  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
  camera.position.set(0, 5, 10);

  const renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(window.innerWidth, window.innerHeight);
  document.body.appendChild(renderer.domElement);

  const controls = new OrbitControls(camera, renderer.domElement);

  // 环境光
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.2);
  scene.add(ambientLight);

  // 创建一个受光照影响的地面
  const planeGeometry = new THREE.PlaneGeometry(20, 20);
  const planeMaterial = new THREE.MeshStandardMaterial({ color: 0x888888, side: THREE.DoubleSide });
  const plane = new THREE.Mesh(planeGeometry, planeMaterial);
  plane.rotation.x = -Math.PI / 2;
  scene.add(plane);

  // 创建粒子
  const particleTexture = new THREE.TextureLoader().load('https://threejs.org/examples/textures/sprites/spark1.png');
  const particles = [];
  const particleMaterial = new THREE.SpriteMaterial({
    map: particleTexture,
    color: 0xff8800, // 橙色粒子
    transparent: true,
    // --- 关键属性在这里 ---
    blending: THREE.AdditiveBlending, // 使用加法混合
    depthWrite: false // 对于透明和混合物体，通常关闭深度写入
  });

  for (let i = 0; i < 100; i++) {
    const sprite = new THREE.Sprite(particleMaterial);
    sprite.position.set(
      (Math.random() - 0.5) * 10,
      (Math.random()) * 5,
      (Math.random() - 0.5) * 10
    );
    sprite.scale.set(0.5, 0.5, 0.5);
    scene.add(sprite);
    particles.push(sprite);
  }

  // GUI 控制
  const gui = new GUI();
  const params = {
    ambientIntensity: ambientLight.intensity,
    blendingMode: 'Additive'
  };

  gui.add(params, 'ambientIntensity', 0, 5).name('Ambient Light').onChange(value => {
    ambientLight.intensity = value;
  });

  gui.add(params, 'blendingMode', ['Additive', 'Normal']).name('Blending').onChange(value => {
    if (value === 'Additive') {
      particleMaterial.blending = THREE.AdditiveBlending;
    } else {
      particleMaterial.blending = THREE.NormalBlending;
    }
    particleMaterial.needsUpdate = true;
  });


  function animate() {
    requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene, camera);
  }
  animate();
</script>
</body>
</html>