import {<PERSON>vas, useThree} from "@react-three/fiber";
import {Physics} from "@react-three/rapier";
import React, {useEffect, useRef, useState} from "react";
import CharacterModel from "./Character/CharacterModel";
import AvatarObject from "../AvatarOrdinalsBrowser/renderAvatar/Avatar/AvatarObject";
import GlobalSpace from "./Global/GlobalSpace";
import SceneManager from "./Scene/SceneManager";
import * as THREE from "three";
import HoverControls from "./HoverControls";
import GlobalSpaceEvent, {CharacterType, GlobalDataKey, SpaceStatus, TransformData} from "./Global/GlobalSpaceEvent";
import Lights from "./Scene/Lights";
import {Perf} from "r3f-perf";
import Fog from "./Scene/Fog";
//@ts-ignore
import * as WebGpg from 'three/webgpu';
import ButlerModel from "./Character/ButlerModel";
import {ButlerData, ButlerUtil} from "./Global/GlobalButlerUtil";
import PlayerManager from "./Character/PlayerManager";
import AvatarData from "../AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData";
import {UsePetManager} from "@/world/Pet/PetManager";


function SetCamera() {
    const {camera, controls} = useThree();

    useEffect(() => {
        camera.layers.enable(999)
        // 设置位置
        camera.position.set(2, 1.5, 2);
        // 设置朝向 (看向场景原点)
        camera.lookAt(new THREE.Vector3(0, 1.0, 0));
    }, [camera]);
    return null;
}

interface IProps {
    debug?: boolean;
    isVisitor: boolean;
    butlerData: ButlerData | null;
    btcAddress: string;
    onClaimPotato?: () => void
}

export default function App({
                                onClaimPotato,
                                butlerData,
                                debug = false,
                                btcAddress,
                                isVisitor,
                            }: IProps) {
    /**
     * Delay physics activate
     */
    const [physics, setPhysics] = useState(false);
    const [movement, setMovement] = useState(false);
    const [status, setStatus] = useState<SpaceStatus>(SpaceStatus.Avatar);
    const [avatarObject, setAvatarObject] = useState<AvatarObject | null>(null);
    const fpsOpen = localStorage.getItem('fpsOpen') === 'true'
    const debugLight = localStorage.getItem('debugLightAndFog') === 'true'
    const debugParticleEditor = localStorage.getItem('debugParticleEditor') === 'true'
    useEffect(() => {
        let oldStatus = status
        const physicsKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(GlobalDataKey.PhysicsDebug, (value) => {
            setPhysics(value);
        })

        const spaceStatusKey = GlobalSpaceEvent.ListenKeyDataChange<SpaceStatus>(GlobalDataKey.SpaceStatus, (value) => {
            setMovement(value === SpaceStatus.Game);
            if (oldStatus === SpaceStatus.NFT && value !== SpaceStatus.NFT) {
                GlobalSpaceEvent.SetDataValue<number>(GlobalDataKey.LookingNftIndex, 0)
            }
            oldStatus = value;
            setStatus(value)
        })

        let stopLoop = false
        GlobalSpace.watchAvatarObjectChange((_avatarObject) => {
            ButlerUtil.setAvatarObj(_avatarObject)
            setAvatarObject(_avatarObject);
            const _renderLoop = function () {
                if (stopLoop) {
                    _avatarObject.unWatchAvatarChange()
                    return
                }
                requestAnimationFrame(_renderLoop);
                if (SpaceStatus.Avatar === oldStatus) {
                    _avatarObject._updateAnimation();
                }
            }
            _renderLoop();

            _avatarObject.watchAvatarChange(() => {
                if (isVisitor) {
                    GlobalSpaceEvent.SetDataValue<AvatarData>(GlobalDataKey.ButlerAvatarData, _avatarObject.avatarData || new AvatarData())
                } else {
                    GlobalSpaceEvent.SetDataValue<AvatarData>(GlobalDataKey.MyAvatarData, _avatarObject.avatarData || new AvatarData())
                    GlobalSpaceEvent.SetDataValue<AvatarData>(GlobalDataKey.ButlerAvatarData, _avatarObject.avatarData || new AvatarData())
                }
            })
        })

        const debugInitData = localStorage.getItem('debugInitData')
        if (debugInitData) {
            const initData = JSON.parse(debugInitData)
            if (initData && initData.mapId && initData.position) {
                GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
                    position: new THREE.Vector3(initData.position.x, initData.position.y, initData.position.z),
                    characterType: CharacterType.Player,
                    sceneType: initData.mapId
                })
            }
        }

        return () => {
            stopLoop = true
            GlobalSpaceEvent.RemoveListener(GlobalDataKey.PhysicsDebug, physicsKey)
            GlobalSpaceEvent.RemoveListener(GlobalDataKey.SpaceStatus, spaceStatusKey)
        }
    }, []);

    const hoverControlsRef: any = useRef(null)

    function handleMouseMove(event: any) {
        if (!hoverControlsRef.current) return
        hoverControlsRef.current.handleMouseMove(event)
    }

    function handleMouseLeave(event: any) {
        if (!hoverControlsRef.current) return
        hoverControlsRef.current.handleMouseLeave(event)
    }

    function handleMouseUp(event: any) {
        if (!hoverControlsRef.current) return
        hoverControlsRef.current.handleMouseUp(event)
    }

    return (
        <>
            <Canvas
              // gl={(canvas) => {
              //     const renderer = new WebGpg.WebGPURenderer({canvas});
              //     const fpsOpen = localStorage.getItem('fpsOpen')
              //     if (fpsOpen === 'true') {
              //         new THREE.WebGLRenderer({canvas});
              //     }
              //     renderer.init().then(() => {
              //         if (fpsOpen === 'true') {
              //             setFpsOpen(true)
              //         }
              //     });
              //     return renderer;
              // }}
                onMouseMove={handleMouseMove}
                onMouseLeave={handleMouseLeave}
                onMouseUp={handleMouseUp}
                shadows
                camera={
                    {
                        fov: 45,
                        near: 0.1,
                        far: 1000,
                        // position: [8.5, 10, 4.6],
                    }
                }>
                {
                    fpsOpen && <Perf position="top-left"/>
                }
                <HoverControls ref={hoverControlsRef}/>
                <SetCamera/>
                <Lights debug={debugLight}/>
                <Fog debug={debugLight}/>
                <Physics debug={physics} timeStep="vary" paused={false}>
                    <ButlerModel butlerData={butlerData}/>
                    {
                        avatarObject && <CharacterModel
                            avatarObj={avatarObject}
                        />
                    }
                    {
                        <PlayerManager/>
                    }
                    <UsePetManager/>
                    <SceneManager
                        onClaimPotato={onClaimPotato}
                    />
                </Physics>
            </Canvas>
        </>
    );
}
