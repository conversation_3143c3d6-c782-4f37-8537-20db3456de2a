import React, {forwardRef, type ForwardRefRenderFunction, useEffect, useImperativeHandle, useRef} from "react";
import * as THREE from "three";
import ParticleNode from "@/world/Particles/ParticleNode";
import {LoaderUtil} from "@/world/Util/LoaderUtil";


const ParticleObject: ForwardRefRenderFunction<THREE.Group | undefined, {
  url: string,
  scale: number,
  time?: number,
  endCallback?: () => void
}> = ({url, scale, time = -1, endCallback}: {
  url: string,
  scale: number,
  time?: number,
  endCallback?: () => void
}, ref) => {
  const groupRef = useRef<THREE.Group>(null);

  useImperativeHandle(ref, () => {
    if (groupRef.current) {
      return groupRef.current;
    }
  }, [groupRef.current]);

  useEffect(() => {
    if (!url.includes('.json')) {
      console.error('url must include .json')
      return
    }

    if (!groupRef.current) {
      return
    }

    const defaultParticleNode = new ParticleNode(time > 0);
    groupRef.current.add(defaultParticleNode);
    defaultParticleNode.scale.set(scale, scale, scale);
    defaultParticleNode.name = 'particleNode';

    let stop = false
    LoaderUtil.loadJson(url, (json) => {
      defaultParticleNode.initFromJson(json, () => {

        // defaultParticleNode.loadFromUrl(url);
        let clock = new THREE.Clock();

        if (time > 0) {
          setTimeout(() => {
            stop = true
            defaultParticleNode.destroy()
            if (endCallback) {
              endCallback()
            }
          }, time)
        }
        const _renderLoop = function () {
          if (stop) {
            return
          }
          defaultParticleNode.update(clock.getDelta());
          requestAnimationFrame(_renderLoop);
        }
        _renderLoop();
      })
    });
    return () => {
      stop = true
      defaultParticleNode.destroy()
      if (endCallback) {
        endCallback()
      }
    };
  }, []);

  return (
    <group ref={groupRef}/>
  );
  // <ParticleObject url={'./particles/effect.json'} scale={0.5}/>
}

export default forwardRef(ParticleObject);