/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, {useEffect, useRef} from 'react'
import {GLTF} from 'three-stdlib'
import Community_01 from "./Community_01";
import GlobalSpaceEvent, {GlobalDataKey} from "../../Global/GlobalSpaceEvent";
import SceneLoader from "../SceneLoader";
import {AppGameApiKey, GetMyPlayer} from "@/world/Character/MyPlayer";
import {LoaderUtil} from "@/world/Util/LoaderUtil";
import {getPizzaActivity} from "@/world/Activity/PizzaActivity";
import {useFrame} from "@react-three/fiber";
import {PizzaPointData} from "@/world/Config/PizzaPointConfig";
import ScenePizza from "@/world/Scene/Community/ScenePizza";
import CanvasUI from "@/world/Scene/Community/CanvasUI";
import ScenePizzaNpc from "@/world/Scene/Community/ScenePizzaNpc";
import {RedPacketConfig, RedPacketData} from "@/world/Config/RedPacketConfig";
import SceneRedPacket from "@/world/Scene/Community/SceneRedPacket";
import {GetGameNetWork} from "@/world/hooks/useNetWork";

export default function Community() {
  const group = useRef<THREE.Group>(null)

  const gameNetWork = GetGameNetWork()
  const [gltf_00, setGLTF_00] = React.useState<GLTF | null>(null)
  const [gltf_01, setGLTF_01] = React.useState<GLTF | null>(null)
  const [packetDataList, setPacketDataList] = React.useState<RedPacketData[]>([])
  const [gameStart, setGameStart] = React.useState<boolean>(false)
  const [pointDataList, setPointDataList] = React.useState<PizzaPointData[]>([])
  const [isEnterRoom, setIsEnterRoom] = React.useState<boolean>(false)
  const [hideList, setHideList] = React.useState<string[]>(['stop'])
  const [pizzaRunning, setPizzaRunning] = React.useState<boolean>(false)
  const pizzaActivity = getPizzaActivity()


  useEffect(() => {
    RedPacketConfig.getInstance().getAllData((list) => {
      setPacketDataList(list)
    })
    gameNetWork.watchRoomStatus((status) => {
      setIsEnterRoom(status.isEnterRoom)
    })
    LoaderUtil.loadGlb('./space/glb/Community_00.glb', (gltf) => {
      setGLTF_00(gltf as any);
    })
  }, []);

  useEffect(() => {
    if (gltf_00) {
      LoaderUtil.loadGlb('./space/glb/Community_01.glb', (gltf) => {
        setGLTF_01(gltf as any);
      })
    }
  }, [gltf_00]);

  const loaded = (haveWall: boolean) => {
    if (haveWall) {
      const myPlayer = GetMyPlayer();
      myPlayer.callAppApi(AppGameApiKey.refreshRank)
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.SceneLoading, false)
    }
  }

  useEffect(() => {
    if (gameStart) {
      const activityData = pizzaActivity.getActivityData()
      const timerList: NodeJS.Timeout[] = []
      const now = Date.now()
      const startRunningTime = Math.max(0, (now - activityData.startTime))
      activityData.pizzaData.forEach(item => {
        if ((item.second + item.duration) * 1000 < startRunningTime) {
          return
        }
        timerList.push(setTimeout(() => {
          setPizzaRunning(true)
          setPointDataList(item.pizzaDataList)
        }, Math.max(0, item.second * 1000 - startRunningTime)))
        timerList.push(setTimeout(() => {
          setPointDataList([])
          setPizzaRunning(false)
        }, (item.second + item.duration) * 1000 - startRunningTime))
        localStorage.setItem('isPizzaActivity', 'true');
        setHideList(['stop', 'A_display', 'A_ditaiA', 'A_LOGO', 'A_logo_01', 'A_logo_02', 'A_logo_03', 'A_logo_04', 'A_p', 'A_rotate_02', 'A_rotate_02_1', 'A_rotate_03', 'A_tuya'])
      })

      return () => {
        timerList.forEach(item => {
          clearTimeout(item)
        })
      }
    } else {
      setPointDataList([])
      setHideList(['stop'])
      localStorage.setItem('isPizzaActivity', 'false');
    }
  }, [gameStart]);

  useFrame(() => {
    const now = Date.now()
    const activityData = pizzaActivity.getActivityData()
    setGameStart(activityData.startTime < now && now <= activityData.endTime && activityData.pizzaData.length > 0)
  })

  return (
    <group ref={group} dispose={null} userData={{camCollisionListener: true}}>
      {
        !gameStart && <CanvasUI/>
      }
      {
        gameStart && <ScenePizzaNpc running={pizzaRunning}/>
      }
      {
        gltf_01 &&
        <Community_01 gltf={gltf_01}/>
      }
      {
        isEnterRoom &&
        packetDataList.length > 0 &&
        packetDataList.map((item, index) => {
          return <SceneRedPacket packetData={item}/>
        })
      }
      <group name="Scene" userData={{camCollisionListener: true}}>
        {
          gltf_00 &&
          <SceneLoader
            loaded={loaded}
            gltf={gltf_00}
            noShadowList={['sky', 'door', 'door001']}
            hideList={hideList}
          />
        }
      </group>
      <>
        {
          pointDataList.length > 0 && pointDataList.map((item, index) => {
            return <ScenePizza pointData={item}/>
          })
        }
      </>
    </group>
  )
}